"""Application configuration"""

import re
from pathlib import Path
from typing import Optional, List, Union

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # Database
    database_url: str = Field(default="sqlite:///./pandoc_api.db")
    
    # Storage paths
    upload_dir: Path = Field(default=Path("storage/uploads"))
    output_dir: Path = Field(default=Path("storage/outputs"))
    template_dir: Path = Field(default=Path("templates"))

    # Additional template folders (comma-separated paths)
    additional_template_dirs: str = Field(default="")

    @field_validator('additional_template_dirs')
    @classmethod
    def validate_additional_template_dirs(cls, v: str) -> str:
        """Validate additional template directories"""
        if not v:
            return v

        # Split by comma and validate each path
        paths = [p.strip() for p in v.split(',') if p.strip()]
        for path_str in paths:
            path = Path(path_str)
            if not path.exists():
                raise ValueError(f"Additional template directory does not exist: {path_str}")

        return v

    @field_validator('max_file_size')
    @classmethod
    def validate_max_file_size(cls, v: Union[int, str]) -> int:
        """Parse max file size from string format (e.g., '100MB') to bytes"""
        if isinstance(v, int):
            return v

        if isinstance(v, str):
            # Parse human-readable format like "100MB", "1GB", etc.
            pattern = r'^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB|TB)$'
            match = re.match(pattern, v.upper().strip())

            if not match:
                raise ValueError(f"Invalid file size format: {v}. Use format like '100MB', '1GB', etc.")

            size_value = float(match.group(1))
            unit = match.group(2)

            # Convert to bytes
            multipliers = {
                'B': 1,
                'KB': 1024,
                'MB': 1024 ** 2,
                'GB': 1024 ** 3,
                'TB': 1024 ** 4
            }

            return int(size_value * multipliers[unit])

        raise ValueError(f"max_file_size must be int or string, got {type(v)}")

    def get_additional_template_dirs(self) -> List[Path]:
        """Get list of additional template directories as Path objects"""
        if not self.additional_template_dirs:
            return []

        paths = [p.strip() for p in self.additional_template_dirs.split(',') if p.strip()]
        return [Path(p) for p in paths]
    
    # API settings
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    debug: bool = Field(default=False)
    
    # Pandoc settings
    pandoc_timeout: int = Field(default=300)  # 5 minutes
    max_file_size: Union[int, str] = Field(default=100 * 1024 * 1024)  # 100MB
    
    # Security
    secret_key: str = Field(default="your-secret-key-here")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure directories exist
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.template_dir.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()

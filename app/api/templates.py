"""Template management API endpoints"""

import json
from typing import Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, Query, UploadFile
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.template_manager import TemplateManager
from app.models.template import TemplateResource, ResourceType
from app.schemas.template import (
    TemplateResourceCreate,
    TemplateResourceResponse,
    TemplateResourceUpdate,
    TemplateResourceList,
    TemplateReloadResponse,
    TemplateSearchRequest
)
from app.utils.file_handler import FileHandler

router = APIRouter(prefix="/templates", tags=["templates"])


@router.post("/", response_model=TemplateResourceResponse)
async def upload_template(
    file: UploadFile = File(..., description="Template file"),
    name: str = Form(..., description="Unique template name"),
    display_name: str = Form(..., description="Display name"),
    resource_type: ResourceType = Form(..., description="Resource type"),
    description: Optional[str] = Form(None, description="Template description"),
    metadata: Optional[str] = Form(None, description="Template metadata (JSON)"),
    db: Session = Depends(get_db)
):
    """Upload a new template or resource file"""

    # Parse metadata if provided
    metadata_dict = {}
    if metadata:
        try:
            metadata_dict = json.loads(metadata)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON in metadata field")

    # Check if name already exists
    existing = db.query(TemplateResource).filter(TemplateResource.name == name).first()
    if existing:
        raise HTTPException(status_code=400, detail="Template name already exists")
    
    # Validate file extension based on resource type
    allowed_extensions = {
        ResourceType.DOCX_TEMPLATE: [".docx"],
        ResourceType.CSL_STYLE: [".csl"],
        ResourceType.LUA_FILTER: [".lua"],
        ResourceType.OTHER: []  # Allow any extension
    }
    
    if resource_type in allowed_extensions and allowed_extensions[resource_type]:
        if not FileHandler.validate_file_extension(file.filename, allowed_extensions[resource_type]):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid file extension for {resource_type}. Allowed: {allowed_extensions[resource_type]}"
            )
    
    try:
        # Save file
        file_path = await FileHandler.save_template_file(file, name, resource_type.value)
        
        # Create database record
        template = TemplateResource(
            name=name,
            display_name=display_name,
            description=description,
            resource_type=resource_type.value,
            file_path=str(file_path),
            file_size=FileHandler.get_file_size(file_path),
            mime_type=FileHandler.get_mime_type(file_path),
            is_builtin=False,
            metadata=metadata_dict
        )
        
        db.add(template)
        db.commit()
        db.refresh(template)
        
        return template
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to upload template: {str(e)}")


@router.get("/", response_model=TemplateResourceList)
async def list_templates(
    resource_type: Optional[ResourceType] = Query(None, description="Filter by resource type"),
    active_only: bool = Query(True, description="Show only active templates"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """List all templates with optional filtering"""
    
    query = db.query(TemplateResource)
    
    # Apply filters
    if resource_type:
        query = query.filter(TemplateResource.resource_type == resource_type.value)
    
    if active_only:
        query = query.filter(TemplateResource.is_active == True)
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * page_size
    resources = query.offset(offset).limit(page_size).all()
    
    return TemplateResourceList(
        resources=resources,
        total=total,
        page=page,
        page_size=page_size
    )


@router.get("/{template_id}", response_model=TemplateResourceResponse)
async def get_template(template_id: int, db: Session = Depends(get_db)):
    """Get template by ID"""
    
    template = db.query(TemplateResource).filter(TemplateResource.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    return template


@router.put("/{template_id}", response_model=TemplateResourceResponse)
async def update_template(
    template_id: int,
    update_data: TemplateResourceUpdate,
    db: Session = Depends(get_db)
):
    """Update template metadata"""
    
    template = db.query(TemplateResource).filter(TemplateResource.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    # Update fields
    update_dict = update_data.model_dump(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(template, field, value)
    
    try:
        db.commit()
        db.refresh(template)
        return template
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to update template: {str(e)}")


@router.delete("/{template_id}")
async def delete_template(template_id: int, db: Session = Depends(get_db)):
    """Delete template"""
    
    template = db.query(TemplateResource).filter(TemplateResource.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="Template not found")
    
    try:
        # Delete file from disk
        FileHandler.delete_file(template.get_absolute_path())
        
        # Delete from database
        db.delete(template)
        db.commit()
        
        return {"message": "Template deleted successfully"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to delete template: {str(e)}")


@router.get("/types/")
async def get_resource_types():
    """Get available resource types"""

    return {
        "resource_types": [
            {
                "value": rt.value,
                "name": rt.value.replace("_", " ").title(),
                "description": {
                    ResourceType.DOCX_TEMPLATE: "Microsoft Word document templates",
                    ResourceType.CSL_STYLE: "Citation Style Language files",
                    ResourceType.LUA_FILTER: "Pandoc Lua filters",
                    ResourceType.OTHER: "Other resource files"
                }[rt]
            }
            for rt in ResourceType
        ]
    }


@router.post("/reload", response_model=TemplateReloadResponse)
async def reload_templates(db: Session = Depends(get_db)):
    """Reload templates from filesystem"""

    try:
        template_manager = TemplateManager(db)
        results = await template_manager.reload_templates()

        return TemplateReloadResponse(
            success=True,
            message="Templates reloaded successfully",
            builtin_loaded=results["builtin_loaded"],
            user_loaded=results["user_loaded"],
            additional_loaded=results["additional_loaded"],
            errors=results["errors"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reload templates: {str(e)}")


@router.post("/search", response_model=TemplateResourceList)
async def search_templates(
    search_request: TemplateSearchRequest,
    db: Session = Depends(get_db)
):
    """Search templates with advanced filtering"""

    try:
        template_manager = TemplateManager(db)
        templates = template_manager.search_templates(
            query=search_request.query,
            resource_type=search_request.resource_type,
            tags=search_request.tags
        )

        # Apply pagination
        total = len(templates)
        offset = (search_request.page - 1) * search_request.page_size
        paginated_templates = templates[offset:offset + search_request.page_size]

        return TemplateResourceList(
            resources=paginated_templates,
            total=total,
            page=search_request.page,
            page_size=search_request.page_size
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Template search failed: {str(e)}")


@router.get("/builtin/")
async def list_builtin_templates(
    resource_type: Optional[ResourceType] = Query(None, description="Filter by resource type"),
    db: Session = Depends(get_db)
):
    """List built-in templates"""

    query = db.query(TemplateResource).filter(
        TemplateResource.is_builtin == True,
        TemplateResource.is_active == True
    )

    if resource_type:
        query = query.filter(TemplateResource.resource_type == resource_type.value)

    templates = query.all()

    return {
        "templates": templates,
        "total": len(templates)
    }
